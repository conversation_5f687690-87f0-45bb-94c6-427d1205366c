{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\api\\web\\testDrive.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\api\\web\\testDrive.js", "mtime": 1765865060981}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\babel.config.js", "mtime": 1753321295652}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1756259109175}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9Temh0YS9EZXNrdG9wL1x1NkQ2OVx1NTkyQS9cdTVCOThcdTdGNTEvYmFvbmVuZy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkVGVzdERyaXZlID0gYWRkVGVzdERyaXZlOwpleHBvcnRzLmRlbFRlc3REcml2ZSA9IGRlbFRlc3REcml2ZTsKZXhwb3J0cy5saXN0VGVzdERyaXZlID0gbGlzdFRlc3REcml2ZTsKZXhwb3J0cy51cGRhdGVUZXN0RHJpdmUgPSB1cGRhdGVUZXN0RHJpdmU7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LliJfooagKZnVuY3Rpb24gbGlzdFRlc3REcml2ZShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS90ZXN0RHJpdmUvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmlrDlop4KZnVuY3Rpb24gYWRkVGVzdERyaXZlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vdGVzdERyaXZlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLkKZnVuY3Rpb24gdXBkYXRlVGVzdERyaXZlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vdGVzdERyaXZlJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpApmdW5jdGlvbiBkZWxUZXN0RHJpdmUoaWRzKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3Rlc3REcml2ZS8nICsgaWRzLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listTestDrive", "query", "request", "url", "method", "params", "addTestDrive", "data", "updateTestDrive", "delTestDrive", "ids"], "sources": ["C:/Users/<USER>/Desktop/浩太/官网/baoneng/ruoyi-ui/src/api/web/testDrive.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询列表\r\nexport function listTestDrive(query) {\r\n  return request({\r\n    url: '/system/testDrive/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 新增\r\nexport function addTestDrive(data) {\r\n  return request({\r\n    url: '/system/testDrive',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改\r\nexport function updateTestDrive(data) {\r\n  return request({\r\n    url: '/system/testDrive',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除\r\nexport function delTestDrive(ids) {\r\n  return request({\r\n    url: '/system/testDrive/' + ids,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACC,GAAG,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGO,GAAG;IAC/BN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}