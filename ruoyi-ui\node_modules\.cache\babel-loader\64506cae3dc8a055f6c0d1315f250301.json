{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765866230901}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\babel.config.js", "mtime": 1753321295652}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_testDrive", "require", "_carModel", "_roadCondition", "name", "data", "list", "open", "title", "form", "queryParams", "carModelList", "roadList", "created", "getList", "loadSelect", "methods", "_this", "listCarModel", "then", "res", "rows", "listRoadCondition", "_this2", "listTestDrive", "handleAdd", "handleEdit", "row", "_objectSpread2", "default", "submitForm", "_this3", "api", "id", "updateTestDrive", "addTestDrive", "$message", "success", "handleDelete", "_this4", "$confirm", "delTestDrive", "handleUploadSuccess", "testDataUrl", "url"], "sources": ["src/views/web/car/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询 -->\r\n    <el-form :model=\"queryParams\" inline>\r\n      <el-form-item label=\"客户\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"客户名称\"/>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"车型\">\r\n        <el-select v-model=\"queryParams.carModelId\" clearable>\r\n          <el-option\r\n            v-for=\"item in carModelList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.modelName\"\r\n            :value=\"item.id\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"路况\">\r\n        <el-select v-model=\"queryParams.roadConditionId\" clearable>\r\n          <el-option\r\n            v-for=\"item in roadList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.conditionName\"\r\n            :value=\"item.id\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-button type=\"primary\" @click=\"getList\">搜索</el-button>\r\n      <el-button type=\"success\" @click=\"handleAdd\">新增</el-button>\r\n    </el-form>\r\n\r\n    <!-- 表格 -->\r\n    <el-table :data=\"list\" border style=\"margin-top: 20px\">\r\n      <el-table-column label=\"客户\" prop=\"customerName\"/>\r\n      <el-table-column label=\"车型\" prop=\"carModelName\"/>\r\n      <el-table-column label=\"路况\" prop=\"roadConditionName\"/>\r\n      <el-table-column label=\"试驾数据\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link v-if=\"scope.row.testDataUrl\"\r\n                   :href=\"scope.row.testDataUrl\"\r\n                   target=\"_blank\">查看</el-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" prop=\"createdAt\"/>\r\n      <el-table-column label=\"操作\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/system/testDrive'\r\nimport { listCarModel } from '@/api/system/carModel'\r\nimport { listRoadCondition } from '@/api/system/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  data() {\r\n    return {\r\n      list: [],\r\n      open: false,\r\n      title: '',\r\n      form: {},\r\n      queryParams: {},\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    loadSelect() {\r\n      listCarModel().then(res => this.carModelList = res.rows)\r\n      listRoadCondition().then(res => this.roadList = res.rows)\r\n    },\r\n    getList() {\r\n      listTestDrive(this.queryParams).then(res => {\r\n        this.list = res.rows\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.form = {}\r\n      this.title = '新增试驾记录'\r\n      this.open = true\r\n    },\r\n    handleEdit(row) {\r\n      this.form = { ...row }\r\n      this.title = '编辑试驾记录'\r\n      this.open = true\r\n    },\r\n    submitForm() {\r\n      const api = this.form.id ? updateTestDrive : addTestDrive\r\n      api(this.form).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该记录吗？', '提示').then(() => {\r\n        delTestDrive(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    },\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;AAoGA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;MACAC,YAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACAD,UAAA,WAAAA,WAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,sBAAA,IAAAC,IAAA,WAAAC,GAAA;QAAA,OAAAH,KAAA,CAAAN,YAAA,GAAAS,GAAA,CAAAC,IAAA;MAAA;MACA,IAAAC,gCAAA,IAAAH,IAAA,WAAAC,GAAA;QAAA,OAAAH,KAAA,CAAAL,QAAA,GAAAQ,GAAA,CAAAC,IAAA;MAAA;IACA;IACAP,OAAA,WAAAA,QAAA;MAAA,IAAAS,MAAA;MACA,IAAAC,wBAAA,OAAAd,WAAA,EAAAS,IAAA,WAAAC,GAAA;QACAG,MAAA,CAAAjB,IAAA,GAAAc,GAAA,CAAAC,IAAA;MACA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA,KAAAhB,IAAA;MACA,KAAAD,KAAA;MACA,KAAAD,IAAA;IACA;IACAmB,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAlB,IAAA,OAAAmB,cAAA,CAAAC,OAAA,MAAAF,GAAA;MACA,KAAAnB,KAAA;MACA,KAAAD,IAAA;IACA;IACAuB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA,QAAAvB,IAAA,CAAAwB,EAAA,GAAAC,0BAAA,GAAAC,uBAAA;MACAH,GAAA,MAAAvB,IAAA,EAAAU,IAAA;QACAY,MAAA,CAAAK,QAAA,CAAAC,OAAA;QACAN,MAAA,CAAAxB,IAAA;QACAwB,MAAA,CAAAjB,OAAA;MACA;IACA;IACAwB,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,KAAAC,QAAA,oBAAArB,IAAA;QACA,IAAAsB,uBAAA,EAAAd,GAAA,CAAAM,EAAA,EAAAd,IAAA;UACAoB,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAzB,OAAA;QACA;MACA;IACA;IACA4B,mBAAA,WAAAA,oBAAAtB,GAAA;MACA,KAAAX,IAAA,CAAAkC,WAAA,GAAAvB,GAAA,CAAAwB,GAAA;IACA;EACA;AACA", "ignoreList": []}]}