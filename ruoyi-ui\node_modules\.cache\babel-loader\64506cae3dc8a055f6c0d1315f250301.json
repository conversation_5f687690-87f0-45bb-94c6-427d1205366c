{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765867159667}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\babel.config.js", "mtime": 1753321295652}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_testDrive", "require", "_carModel", "_roadCondition", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "list", "title", "open", "queryParams", "pageNum", "pageSize", "customerName", "carModelId", "roadConditionId", "date<PERSON><PERSON><PERSON>", "form", "carModelList", "roadList", "created", "getList", "loadSelect", "methods", "_this", "params", "listTestDrive", "then", "response", "rows", "cancel", "reset", "id", "testDataUrl", "createdAt", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_objectSpread2", "default", "submitForm", "_this2", "$refs", "validate", "valid", "updateTestDrive", "$modal", "msgSuccess", "addTestDrive", "handleDelete", "_this3", "confirm", "delTestDrive", "catch", "handleUploadSuccess", "res", "url", "_this4", "listCarModel", "listRoadCondition"], "sources": ["src/views/web/car/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"请输入客户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"车型\" prop=\"carModelId\">\r\n        <el-select v-model=\"queryParams.carModelId\" placeholder=\"请选择车型\" clearable>\r\n          <el-option\r\n            v-for=\"item in carModelList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.modelName\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"路况\" prop=\"roadConditionId\">\r\n        <el-select v-model=\"queryParams.roadConditionId\" placeholder=\"请选择路况\" clearable>\r\n          <el-option\r\n            v-for=\"item in roadList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.conditionName\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          size=\"small\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"客户\" align=\"center\" prop=\"customerName\" />\r\n      <el-table-column label=\"车型\" align=\"center\" prop=\"carModelName\" />\r\n      <el-table-column label=\"路况\" align=\"center\" prop=\"roadConditionName\" />\r\n      <el-table-column label=\"试驾数据\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link v-if=\"scope.row.testDataUrl\"\r\n                   :href=\"scope.row.testDataUrl\"\r\n                   target=\"_blank\">查看</el-link>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'\r\nimport { listCarModel } from '@/api/web/carModel'\r\nimport { listRoadCondition } from '@/api/web/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 试驾记录表格数据\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: null,\r\n        carModelId: null,\r\n        roadConditionId: null,\r\n      },\r\n      // 时间范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 下拉选项\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    /** 查询试驾记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      this.queryParams.params = {};\r\n      if (null != this.dateRange && '' != this.dateRange) {\r\n        this.queryParams.params[\"beginTime\"] = this.dateRange[0];\r\n        this.queryParams.params[\"endTime\"] = this.dateRange[1];\r\n      }\r\n      listTestDrive(this.queryParams).then(response => {\r\n        this.list = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        customerName: null,\r\n        carModelId: null,\r\n        roadConditionId: null,\r\n        testDataUrl: null,\r\n        createdAt: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加试驾记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.form = { ...row };\r\n      this.open = true;\r\n      this.title = \"修改试驾记录\";\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateTestDrive(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTestDrive(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除试驾记录编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delTestDrive(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 上传成功回调 */\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url;\r\n    },\r\n    /** 加载下拉选项 */\r\n    loadSelect() {\r\n      listCarModel().then(res => {\r\n        this.carModelList = res.rows || []\r\n      })\r\n      listRoadCondition().then(res => {\r\n        this.roadList = res.rows || []\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA8KA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACA,eACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAvB,OAAA;MACA,KAAAS,WAAA,CAAAe,MAAA;MACA,iBAAAT,SAAA,eAAAA,SAAA;QACA,KAAAN,WAAA,CAAAe,MAAA,qBAAAT,SAAA;QACA,KAAAN,WAAA,CAAAe,MAAA,mBAAAT,SAAA;MACA;MACA,IAAAU,wBAAA,OAAAhB,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAjB,IAAA,GAAAqB,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAlB,KAAA,GAAAsB,QAAA,CAAAtB,KAAA;QACAkB,KAAA,CAAAvB,OAAA;MACA;IACA;IACA,WACA6B,MAAA,WAAAA,OAAA;MACA,KAAArB,IAAA;MACA,KAAAsB,KAAA;IACA;IACA,WACAA,KAAA,WAAAA,MAAA;MACA,KAAAd,IAAA;QACAe,EAAA;QACAnB,YAAA;QACAC,UAAA;QACAC,eAAA;QACAkB,WAAA;QACAC,SAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACA,aACAgB,UAAA,WAAAA,WAAA;MACA,KAAArB,SAAA;MACA,KAAAmB,SAAA;MACA,KAAAC,WAAA;IACA;IACA,cACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArC,GAAA,GAAAqC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,EAAA;MAAA;MACA,KAAA7B,MAAA,GAAAoC,SAAA,CAAAG,MAAA;MACA,KAAAtC,QAAA,IAAAmC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAZ,KAAA;MACA,KAAAtB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAoC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAd,KAAA;MACA,KAAAd,IAAA,OAAA6B,cAAA,CAAAC,OAAA,MAAAF,GAAA;MACA,KAAApC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,WACAwC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhC,IAAA,CAAAe,EAAA;YACA,IAAAqB,0BAAA,EAAAJ,MAAA,CAAAhC,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAqB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAA5B,OAAA;YACA;UACA;YACA,IAAAmC,uBAAA,EAAAP,MAAA,CAAAhC,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAqB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAA5B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAoC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAxD,GAAA,GAAA2C,GAAA,CAAAb,EAAA,SAAA9B,GAAA;MACA,KAAAoD,MAAA,CAAAK,OAAA,oBAAAzD,GAAA,aAAAyB,IAAA;QACA,WAAAiC,uBAAA,EAAA1D,GAAA;MACA,GAAAyB,IAAA;QACA+B,MAAA,CAAArC,OAAA;QACAqC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAA9C,IAAA,CAAAgB,WAAA,GAAA8B,GAAA,CAAAC,GAAA;IACA;IACA,aACA1C,UAAA,WAAAA,WAAA;MAAA,IAAA2C,MAAA;MACA,IAAAC,sBAAA,IAAAvC,IAAA,WAAAoC,GAAA;QACAE,MAAA,CAAA/C,YAAA,GAAA6C,GAAA,CAAAlC,IAAA;MACA;MACA,IAAAsC,gCAAA,IAAAxC,IAAA,WAAAoC,GAAA;QACAE,MAAA,CAAA9C,QAAA,GAAA4C,GAAA,CAAAlC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}