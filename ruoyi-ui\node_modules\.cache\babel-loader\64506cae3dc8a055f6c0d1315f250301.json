{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765866862858}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\babel.config.js", "mtime": 1753321295652}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_testDrive", "require", "_carModel", "_roadCondition", "name", "data", "list", "loading", "total", "selectedRows", "open", "title", "form", "queryParams", "pageNum", "pageSize", "customerName", "undefined", "carModelId", "roadConditionId", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "carModelList", "roadList", "created", "getList", "loadSelect", "methods", "_this", "listCarModel", "then", "res", "rows", "listRoadCondition", "_this2", "length", "listTestDrive", "catch", "handleQuery", "reset<PERSON><PERSON>y", "$refs", "queryForm", "resetFields", "handleSelectionChange", "selection", "handleSizeChange", "val", "handleCurrentChange", "getRoadConditionType", "roadCondition", "typeMap", "handleAdd", "handleEdit", "row", "_objectSpread2", "default", "submitForm", "_this3", "api", "id", "updateTestDrive", "addTestDrive", "$message", "success", "handleDelete", "_this4", "$confirm", "delTestDrive", "handleUploadSuccess", "testDataUrl", "url", "handleBatchEdit", "warning", "handleBatchDelete", "_this5", "ids", "map", "concat", "confirmButtonText", "cancelButtonText", "type", "Promise", "all"], "sources": ["src/views/web/car/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询表单 -->\r\n    <div class=\"search-form-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" class=\"search-form\">\r\n        <el-form-item label=\"客户\" prop=\"customerName\">\r\n          <el-input\r\n            v-model=\"queryParams.customerName\"\r\n            placeholder=\"请输入客户名称\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\" prop=\"carModelId\">\r\n          <el-select\r\n            v-model=\"queryParams.carModelId\"\r\n            placeholder=\"请选择车型\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\" prop=\"roadConditionId\">\r\n          <el-select\r\n            v-model=\"queryParams.roadConditionId\"\r\n            placeholder=\"请选择路况\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"创建时间\" prop=\"dateRange\">\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"—\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 240px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item class=\"search-buttons\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 操作按钮 -->\r\n      <div class=\"action-buttons\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"success\" icon=\"el-icon-edit\" :disabled=\"!selectedRows.length\" @click=\"handleBatchEdit\">修改</el-button>\r\n        <el-button type=\"danger\" icon=\"el-icon-delete\" :disabled=\"!selectedRows.length\" @click=\"handleBatchDelete\">删除</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-container\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        border\r\n        stripe\r\n        @selection-change=\"handleSelectionChange\"\r\n        class=\"data-table\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"客户\" prop=\"customerName\" min-width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"车型\" prop=\"carModelName\" min-width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"路况\" prop=\"roadConditionName\" min-width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getRoadConditionType(scope.row.roadConditionName)\">\r\n              {{ scope.row.roadConditionName }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" prop=\"createdAt\" width=\"180\" align=\"center\" />\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleEdit(scope.row)\"\r\n              class=\"edit-btn\"\r\n            >\r\n              修改\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              class=\"delete-btn\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页组件 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"queryParams.pageNum\"\r\n          :page-sizes=\"[5, 10, 20, 50]\"\r\n          :page-size=\"queryParams.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'\r\nimport { listCarModel } from '@/api/web/carModel'\r\nimport { listRoadCondition } from '@/api/web/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  data() {\r\n    return {\r\n      // 表格数据\r\n      list: [],\r\n      loading: false,\r\n      total: 0,\r\n      selectedRows: [],\r\n      // 弹窗相关\r\n      open: false,\r\n      title: '',\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: undefined,\r\n        carModelId: undefined,\r\n        roadConditionId: undefined,\r\n        startDate: undefined,\r\n        endDate: undefined\r\n      },\r\n      // 时间范围\r\n      dateRange: [],\r\n      // 下拉选项\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    /** 加载下拉选项 */\r\n    loadSelect() {\r\n      listCarModel().then(res => {\r\n        this.carModelList = res.rows || []\r\n      })\r\n      listRoadCondition().then(res => {\r\n        this.roadList = res.rows || []\r\n      })\r\n    },\r\n\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true\r\n      // 处理时间范围\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        this.queryParams.startDate = this.dateRange[0]\r\n        this.queryParams.endDate = this.dateRange[1]\r\n      } else {\r\n        this.queryParams.startDate = undefined\r\n        this.queryParams.endDate = undefined\r\n      }\r\n\r\n      listTestDrive(this.queryParams).then(res => {\r\n        this.list = res.rows || []\r\n        this.total = res.total || 0\r\n        this.loading = false\r\n      }).catch(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: undefined,\r\n        carModelId: undefined,\r\n        roadConditionId: undefined,\r\n        startDate: undefined,\r\n        endDate: undefined\r\n      }\r\n      this.$refs.queryForm.resetFields()\r\n      this.getList()\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection\r\n    },\r\n\r\n    /** 分页相关 */\r\n    handleSizeChange(val) {\r\n      this.queryParams.pageSize = val\r\n      this.getList()\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.queryParams.pageNum = val\r\n      this.getList()\r\n    },\r\n\r\n    /** 获取路况标签类型 */\r\n    getRoadConditionType(roadCondition) {\r\n      const typeMap = {\r\n        '高速路况': 'success',\r\n        '城市路况': 'primary',\r\n        '山路路况': 'warning',\r\n        '恶劣路况': 'danger'\r\n      }\r\n      return typeMap[roadCondition] || 'info'\r\n    },\r\n    handleAdd() {\r\n      this.form = {}\r\n      this.title = '新增试驾记录'\r\n      this.open = true\r\n    },\r\n    handleEdit(row) {\r\n      this.form = { ...row }\r\n      this.title = '编辑试驾记录'\r\n      this.open = true\r\n    },\r\n    submitForm() {\r\n      const api = this.form.id ? updateTestDrive : addTestDrive\r\n      api(this.form).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该记录吗？', '提示').then(() => {\r\n        delTestDrive(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    },\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url\r\n    },\r\n\r\n    /** 批量修改 */\r\n    handleBatchEdit() {\r\n      if (this.selectedRows.length !== 1) {\r\n        this.$message.warning('请选择一条记录进行修改')\r\n        return\r\n      }\r\n      this.handleEdit(this.selectedRows[0])\r\n    },\r\n\r\n    /** 批量删除 */\r\n    handleBatchDelete() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请至少选择一条记录')\r\n        return\r\n      }\r\n\r\n      const ids = this.selectedRows.map(row => row.id)\r\n      this.$confirm(`确认删除选中的${ids.length}条记录吗？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 这里需要调用批量删除的API\r\n        Promise.all(ids.map(id => delTestDrive(id))).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 搜索表单样式 */\r\n.search-form-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n\r\n  .search-form {\r\n    .el-form-item {\r\n      margin-bottom: 16px;\r\n      margin-right: 20px;\r\n\r\n      .el-form-item__label {\r\n        font-weight: 500;\r\n        color: #606266;\r\n      }\r\n    }\r\n\r\n    .search-buttons {\r\n      .el-button {\r\n        margin-left: 10px;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    margin-top: 16px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #ebeef5;\r\n\r\n    .el-button {\r\n      margin-right: 10px;\r\n\r\n      &.el-button--primary {\r\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--success {\r\n        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--danger {\r\n        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);\r\n        border: none;\r\n      }\r\n\r\n      &:hover {\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n\r\n  .data-table {\r\n    border: none;\r\n\r\n    ::v-deep .el-table__header-wrapper {\r\n      .el-table__header {\r\n        th {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          font-weight: 600;\r\n          border: none;\r\n\r\n          .cell {\r\n            padding: 12px 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    ::v-deep .el-table__body-wrapper {\r\n      .el-table__body {\r\n        tr {\r\n          &:hover {\r\n            background-color: #f8f9ff;\r\n          }\r\n\r\n          td {\r\n            border-bottom: 1px solid #f0f2f5;\r\n\r\n            .cell {\r\n              padding: 12px 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .edit-btn {\r\n      color: #409eff;\r\n\r\n      &:hover {\r\n        color: #66b1ff;\r\n        background-color: #ecf5ff;\r\n      }\r\n    }\r\n\r\n    .delete-btn {\r\n      color: #f56c6c;\r\n\r\n      &:hover {\r\n        color: #f78989;\r\n        background-color: #fef0f0;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 分页样式 */\r\n  .pagination-container {\r\n    padding: 20px;\r\n    text-align: right;\r\n    border-top: 1px solid #f0f2f5;\r\n    background-color: #fafbfc;\r\n\r\n    ::v-deep .el-pagination {\r\n      .el-pager li {\r\n        &.active {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          border-radius: 4px;\r\n        }\r\n\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n\r\n      .btn-prev,\r\n      .btn-next {\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form-container {\r\n    .search-form {\r\n      .el-form-item {\r\n        margin-right: 0;\r\n        width: 100%;\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-date-picker {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .action-buttons {\r\n      .el-button {\r\n        margin-bottom: 10px;\r\n        width: 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .table-container {\r\n    .data-table {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .pagination-container {\r\n      text-align: center;\r\n\r\n      ::v-deep .el-pagination {\r\n        .el-pagination__sizes,\r\n        .el-pagination__jump {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAuLA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,IAAA;MACAC,OAAA;MACAC,KAAA;MACAC,YAAA;MACA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,SAAA;QACAC,UAAA,EAAAD,SAAA;QACAE,eAAA,EAAAF,SAAA;QACAG,SAAA,EAAAH,SAAA;QACAI,OAAA,EAAAJ;MACA;MACA;MACAK,SAAA;MACA;MACAC,YAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACA,aACAD,UAAA,WAAAA,WAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,sBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAN,YAAA,GAAAS,GAAA,CAAAC,IAAA;MACA;MACA,IAAAC,gCAAA,IAAAH,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAL,QAAA,GAAAQ,GAAA,CAAAC,IAAA;MACA;IACA;IAEA,WACAP,OAAA,WAAAA,QAAA;MAAA,IAAAS,MAAA;MACA,KAAA5B,OAAA;MACA;MACA,SAAAe,SAAA,SAAAA,SAAA,CAAAc,MAAA;QACA,KAAAvB,WAAA,CAAAO,SAAA,QAAAE,SAAA;QACA,KAAAT,WAAA,CAAAQ,OAAA,QAAAC,SAAA;MACA;QACA,KAAAT,WAAA,CAAAO,SAAA,GAAAH,SAAA;QACA,KAAAJ,WAAA,CAAAQ,OAAA,GAAAJ,SAAA;MACA;MAEA,IAAAoB,wBAAA,OAAAxB,WAAA,EAAAkB,IAAA,WAAAC,GAAA;QACAG,MAAA,CAAA7B,IAAA,GAAA0B,GAAA,CAAAC,IAAA;QACAE,MAAA,CAAA3B,KAAA,GAAAwB,GAAA,CAAAxB,KAAA;QACA2B,MAAA,CAAA5B,OAAA;MACA,GAAA+B,KAAA;QACAH,MAAA,CAAA5B,OAAA;MACA;IACA;IAEA,aACAgC,WAAA,WAAAA,YAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAAY,OAAA;IACA;IAEA,aACAc,UAAA,WAAAA,WAAA;MACA,KAAAlB,SAAA;MACA,KAAAT,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,SAAA;QACAC,UAAA,EAAAD,SAAA;QACAE,eAAA,EAAAF,SAAA;QACAG,SAAA,EAAAH,SAAA;QACAI,OAAA,EAAAJ;MACA;MACA,KAAAwB,KAAA,CAAAC,SAAA,CAAAC,WAAA;MACA,KAAAjB,OAAA;IACA;IAEA,cACAkB,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApC,YAAA,GAAAoC,SAAA;IACA;IAEA,WACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAlC,WAAA,CAAAE,QAAA,GAAAgC,GAAA;MACA,KAAArB,OAAA;IACA;IAEAsB,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAlC,WAAA,CAAAC,OAAA,GAAAiC,GAAA;MACA,KAAArB,OAAA;IACA;IAEA,eACAuB,oBAAA,WAAAA,qBAAAC,aAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,aAAA;IACA;IACAE,SAAA,WAAAA,UAAA;MACA,KAAAxC,IAAA;MACA,KAAAD,KAAA;MACA,KAAAD,IAAA;IACA;IACA2C,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAA1C,IAAA,OAAA2C,cAAA,CAAAC,OAAA,MAAAF,GAAA;MACA,KAAA3C,KAAA;MACA,KAAAD,IAAA;IACA;IACA+C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,GAAA,QAAA/C,IAAA,CAAAgD,EAAA,GAAAC,0BAAA,GAAAC,uBAAA;MACAH,GAAA,MAAA/C,IAAA,EAAAmB,IAAA;QACA2B,MAAA,CAAAK,QAAA,CAAAC,OAAA;QACAN,MAAA,CAAAhD,IAAA;QACAgD,MAAA,CAAAhC,OAAA;MACA;IACA;IACAuC,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,KAAAC,QAAA,oBAAApC,IAAA;QACA,IAAAqC,uBAAA,EAAAd,GAAA,CAAAM,EAAA,EAAA7B,IAAA;UACAmC,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAxC,OAAA;QACA;MACA;IACA;IACA2C,mBAAA,WAAAA,oBAAArC,GAAA;MACA,KAAApB,IAAA,CAAA0D,WAAA,GAAAtC,GAAA,CAAAuC,GAAA;IACA;IAEA,WACAC,eAAA,WAAAA,gBAAA;MACA,SAAA/D,YAAA,CAAA2B,MAAA;QACA,KAAA2B,QAAA,CAAAU,OAAA;QACA;MACA;MACA,KAAApB,UAAA,MAAA5C,YAAA;IACA;IAEA,WACAiE,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAlE,YAAA,CAAA2B,MAAA;QACA,KAAA2B,QAAA,CAAAU,OAAA;QACA;MACA;MAEA,IAAAG,GAAA,QAAAnE,YAAA,CAAAoE,GAAA,WAAAvB,GAAA;QAAA,OAAAA,GAAA,CAAAM,EAAA;MAAA;MACA,KAAAO,QAAA,8CAAAW,MAAA,CAAAF,GAAA,CAAAxC,MAAA;QACA2C,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAlD,IAAA;QACA;QACAmD,OAAA,CAAAC,GAAA,CAAAP,GAAA,CAAAC,GAAA,WAAAjB,EAAA;UAAA,WAAAQ,uBAAA,EAAAR,EAAA;QAAA,IAAA7B,IAAA;UACA4C,MAAA,CAAAZ,QAAA,CAAAC,OAAA;UACAW,MAAA,CAAAjD,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}