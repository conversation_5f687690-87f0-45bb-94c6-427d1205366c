{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\api\\web\\carModel.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\api\\web\\carModel.js", "mtime": 1765865633436}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\babel.config.js", "mtime": 1753321295652}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1756259109175}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9Temh0YS9EZXNrdG9wL1x1NkQ2OVx1NTkyQS9cdTVCOThcdTdGNTEvYmFvbmVuZy9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkQ2FyTW9kZWwgPSBhZGRDYXJNb2RlbDsKZXhwb3J0cy5kZWxDYXJNb2RlbCA9IGRlbENhck1vZGVsOwpleHBvcnRzLmxpc3RDYXJNb2RlbCA9IGxpc3RDYXJNb2RlbDsKZXhwb3J0cy51cGRhdGVDYXJNb2RlbCA9IHVwZGF0ZUNhck1vZGVsOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKZnVuY3Rpb24gbGlzdENhck1vZGVsKCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9jYXJNb2RlbC9saXN0JywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQpmdW5jdGlvbiBhZGRDYXJNb2RlbChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2Nhck1vZGVsJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CmZ1bmN0aW9uIHVwZGF0ZUNhck1vZGVsKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vY2FyTW9kZWwnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQpmdW5jdGlvbiBkZWxDYXJNb2RlbChpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9jYXJNb2RlbC8nICsgaWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listCarModel", "request", "url", "method", "addCarModel", "data", "updateCarModel", "delCarModel", "id"], "sources": ["C:/Users/<USER>/Desktop/浩太/官网/baoneng/ruoyi-ui/src/api/web/carModel.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\nexport function listCarModel() {\r\n  return request({\r\n    url: '/system/carModel/list',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function addCarModel(data) {\r\n  return request({\r\n    url: '/system/carModel',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function updateCarModel(data) {\r\n  return request({\r\n    url: '/system/carModel',\r\n    method: 'put',\r\n    data\r\n  })\r\n}\r\n\r\nexport function delCarModel(id) {\r\n  return request({\r\n    url: '/system/carModel/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEO,SAASC,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEO,SAASE,WAAWA,CAACC,EAAE,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGM,EAAE;IAC7BL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}