{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\Hamburger\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\Hamburger\\index.vue", "mtime": 1739929493643}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdIYW1idXJnZXInLA0KICBwcm9wczogew0KICAgIGlzQWN0aXZlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICB0b2dnbGVDbGljaygpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ3RvZ2dsZUNsaWNrJykNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Hamburger", "sourcesContent": ["<template>\r\n  <div style=\"padding: 0 15px;\" @click=\"toggleClick\">\r\n    <svg\r\n      :class=\"{'is-active':isActive}\"\r\n      class=\"hamburger\"\r\n      viewBox=\"0 0 1024 1024\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"64\"\r\n      height=\"64\"\r\n    >\r\n      <path d=\"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z\" />\r\n    </svg>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Hamburger',\r\n  props: {\r\n    isActive: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  methods: {\r\n    toggleClick() {\r\n      this.$emit('toggleClick')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.hamburger {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.hamburger.is-active {\r\n  transform: rotate(180deg);\r\n}\r\n</style>\r\n"]}]}