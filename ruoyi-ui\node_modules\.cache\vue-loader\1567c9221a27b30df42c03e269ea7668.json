{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\RuoYi\\Git\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\RuoYi\\Git\\index.vue", "mtime": 1739929493648}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdSdW9ZaUdpdCcsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVybDogJ2h0dHBzOi8vZ2l0ZWUuY29tL3lfcHJvamVjdC9SdW9ZaS1WdWUnDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ290bygpIHsNCiAgICAgIHdpbmRvdy5vcGVuKHRoaXMudXJsKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RuoYi/Git", "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon icon-class=\"github\" @click=\"goto\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RuoYiGit',\r\n  data() {\r\n    return {\r\n      url: 'https://gitee.com/y_project/RuoYi-Vue'\r\n    }\r\n  },\r\n  methods: {\r\n    goto() {\r\n      window.open(this.url)\r\n    }\r\n  }\r\n}\r\n</script>"]}]}