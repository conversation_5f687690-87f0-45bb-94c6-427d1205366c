{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\TopNav\\index.vue", "mtime": 1739929493651}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\">\r\n        <svg-icon\r\n        v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\r\n        :icon-class=\"item.meta.icon\"/>\r\n        {{ item.meta.title }}\r\n      </el-menu-item>\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" v-if=\"topMenus.length > visibleNumber\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          :index=\"item.path\"\r\n          :key=\"index\"\r\n          v-if=\"index >= visibleNumber\">\r\n          <svg-icon\r\n            v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\r\n            :icon-class=\"item.meta.icon\"/>\r\n          {{ item.meta.title }}\r\n        </el-menu-item>\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from \"@/router\";\r\n\r\n// 隐藏侧边栏路由\r\nconst hideList = ['/index', '/user/profile'];\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined\r\n    };\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      let topMenus = [];\r\n      this.routers.map((menu) => {\r\n        if (menu.hidden !== true) {\r\n          // 兼容顶部栏一级菜单内部跳转\r\n          if (menu.path === \"/\") {\r\n            topMenus.push(menu.children[0]);\r\n          } else {\r\n            topMenus.push(menu);\r\n          }\r\n        }\r\n      });\r\n      return topMenus;\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters;\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = [];\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            if(router.path === \"/\") {\r\n              router.children[item].path = \"/\" + router.children[item].path;\r\n            } else {\r\n              if(!this.ishttp(router.children[item].path)) {\r\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\r\n              }\r\n            }\r\n            router.children[item].parentPath = router.path;\r\n          }\r\n          childrenMenus.push(router.children[item]);\r\n        }\r\n      });\r\n      return constantRoutes.concat(childrenMenus);\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path;\r\n      let activePath = path;\r\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\r\n        const tmpPath = path.substring(1, path.length);\r\n        activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\r\n        if (!this.$route.meta.link) {\r\n          this.$store.dispatch('app/toggleSideBarHide', false);\r\n        }\r\n      } else if(!this.$route.children) {\r\n        activePath = path;\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n      this.activeRoutes(activePath);\r\n      return activePath;\r\n    },\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber();\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width / 3;\r\n      this.visibleNumber = parseInt(width / 85);\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      this.currentIndex = key;\r\n      const route = this.routers.find(item => item.path === key);\r\n      if (this.ishttp(key)) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, \"_blank\");\r\n      } else if (!route || !route.children) {\r\n        // 没有子路由路径内部打开\r\n        const routeMenu = this.childrenMenus.find(item => item.path === key);\r\n        if (routeMenu && routeMenu.query) {\r\n          let query = JSON.parse(routeMenu.query);\r\n          this.$router.push({ path: key, query: query });\r\n        } else {\r\n          this.$router.push({ path: key });\r\n        }\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      } else {\r\n        // 显示左侧联动菜单\r\n        this.activeRoutes(key);\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = [];\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\r\n            routes.push(item);\r\n          }\r\n        });\r\n      }\r\n      if(routes.length > 0) {\r\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\r\n      } else {\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n    },\r\n    ishttp(url) {\r\n      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.topmenu-container.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\r\n  border-bottom: 2px solid #{'var(--theme)'} !important;\r\n  color: #303133;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n</style>\r\n"]}]}