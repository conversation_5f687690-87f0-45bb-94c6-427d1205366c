{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765867159667}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0VGVzdERyaXZlLCBhZGRUZXN0RHJpdmUsIHVwZGF0ZVRlc3REcml2ZSwgZGVsVGVzdERyaXZlIH0gZnJvbSAnQC9hcGkvd2ViL3Rlc3REcml2ZScNCmltcG9ydCB7IGxpc3RDYXJNb2RlbCB9IGZyb20gJ0AvYXBpL3dlYi9jYXJNb2RlbCcNCmltcG9ydCB7IGxpc3RSb2FkQ29uZGl0aW9uIH0gZnJvbSAnQC9hcGkvd2ViL3JvYWRDb25kaXRpb24nDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1Rlc3REcml2ZScsDQogIGNvbXBvbmVudHM6IHsNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOivlempvuiusOW9leihqOagvOaVsOaNrg0KICAgICAgbGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgY3VzdG9tZXJOYW1lOiBudWxsLA0KICAgICAgICBjYXJNb2RlbElkOiBudWxsLA0KICAgICAgICByb2FkQ29uZGl0aW9uSWQ6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g5pe26Ze06IyD5Zu0DQogICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOS4i+aLiemAiemhuQ0KICAgICAgY2FyTW9kZWxMaXN0OiBbXSwNCiAgICAgIHJvYWRMaXN0OiBbXQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICAgIHRoaXMubG9hZFNlbGVjdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6K+V6am+6K6w5b2V5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcyA9IHt9Ow0KICAgICAgaWYgKG51bGwgIT0gdGhpcy5kYXRlUmFuZ2UgJiYgJycgIT0gdGhpcy5kYXRlUmFuZ2UpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXNbImJlZ2luVGltZSJdID0gdGhpcy5kYXRlUmFuZ2VbMF07DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zWyJlbmRUaW1lIl0gPSB0aGlzLmRhdGVSYW5nZVsxXTsNCiAgICAgIH0NCiAgICAgIGxpc3RUZXN0RHJpdmUodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlj5bmtojmjInpkq4gKi8NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8qKiDooajljZXph43nva4gKi8NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGN1c3RvbWVyTmFtZTogbnVsbCwNCiAgICAgICAgY2FyTW9kZWxJZDogbnVsbCwNCiAgICAgICAgcm9hZENvbmRpdGlvbklkOiBudWxsLA0KICAgICAgICB0ZXN0RGF0YVVybDogbnVsbCwNCiAgICAgICAgY3JlYXRlZEF0OiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8qKiDlpJrpgInmoYbpgInkuK3mlbDmja4gKi8NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6K+V6am+6K6w5b2VIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9Ow0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56K+V6am+6K6w5b2VIjsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVUZXN0RHJpdmUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRUZXN0RHJpdmUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6K+V6am+6K6w5b2V57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxUZXN0RHJpdmUoaWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5LiK5Lyg5oiQ5Yqf5Zue6LCDICovDQogICAgaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXMpIHsNCiAgICAgIHRoaXMuZm9ybS50ZXN0RGF0YVVybCA9IHJlcy51cmw7DQogICAgfSwNCiAgICAvKiog5Yqg6L295LiL5ouJ6YCJ6aG5ICovDQogICAgbG9hZFNlbGVjdCgpIHsNCiAgICAgIGxpc3RDYXJNb2RlbCgpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5jYXJNb2RlbExpc3QgPSByZXMucm93cyB8fCBbXQ0KICAgICAgfSkNCiAgICAgIGxpc3RSb2FkQ29uZGl0aW9uKCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLnJvYWRMaXN0ID0gcmVzLnJvd3MgfHwgW10NCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/web/car", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"请输入客户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"车型\" prop=\"carModelId\">\r\n        <el-select v-model=\"queryParams.carModelId\" placeholder=\"请选择车型\" clearable>\r\n          <el-option\r\n            v-for=\"item in carModelList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.modelName\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"路况\" prop=\"roadConditionId\">\r\n        <el-select v-model=\"queryParams.roadConditionId\" placeholder=\"请选择路况\" clearable>\r\n          <el-option\r\n            v-for=\"item in roadList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.conditionName\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          size=\"small\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"客户\" align=\"center\" prop=\"customerName\" />\r\n      <el-table-column label=\"车型\" align=\"center\" prop=\"carModelName\" />\r\n      <el-table-column label=\"路况\" align=\"center\" prop=\"roadConditionName\" />\r\n      <el-table-column label=\"试驾数据\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link v-if=\"scope.row.testDataUrl\"\r\n                   :href=\"scope.row.testDataUrl\"\r\n                   target=\"_blank\">查看</el-link>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'\r\nimport { listCarModel } from '@/api/web/carModel'\r\nimport { listRoadCondition } from '@/api/web/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 试驾记录表格数据\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: null,\r\n        carModelId: null,\r\n        roadConditionId: null,\r\n      },\r\n      // 时间范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 下拉选项\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    /** 查询试驾记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      this.queryParams.params = {};\r\n      if (null != this.dateRange && '' != this.dateRange) {\r\n        this.queryParams.params[\"beginTime\"] = this.dateRange[0];\r\n        this.queryParams.params[\"endTime\"] = this.dateRange[1];\r\n      }\r\n      listTestDrive(this.queryParams).then(response => {\r\n        this.list = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        customerName: null,\r\n        carModelId: null,\r\n        roadConditionId: null,\r\n        testDataUrl: null,\r\n        createdAt: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加试驾记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.form = { ...row };\r\n      this.open = true;\r\n      this.title = \"修改试驾记录\";\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateTestDrive(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTestDrive(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除试驾记录编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delTestDrive(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 上传成功回调 */\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url;\r\n    },\r\n    /** 加载下拉选项 */\r\n    loadSelect() {\r\n      listCarModel().then(res => {\r\n        this.carModelList = res.rows || []\r\n      })\r\n      listRoadCondition().then(res => {\r\n        this.roadList = res.rows || []\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}