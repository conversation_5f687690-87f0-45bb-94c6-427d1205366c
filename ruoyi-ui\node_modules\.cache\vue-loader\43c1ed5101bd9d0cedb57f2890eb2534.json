{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765866862858}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0VGVzdERyaXZlLCBhZGRUZXN0RHJpdmUsIHVwZGF0ZVRlc3REcml2ZSwgZGVsVGVzdERyaXZlIH0gZnJvbSAnQC9hcGkvd2ViL3Rlc3REcml2ZScNCmltcG9ydCB7IGxpc3RDYXJNb2RlbCB9IGZyb20gJ0AvYXBpL3dlYi9jYXJNb2RlbCcNCmltcG9ydCB7IGxpc3RSb2FkQ29uZGl0aW9uIH0gZnJvbSAnQC9hcGkvd2ViL3JvYWRDb25kaXRpb24nDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1Rlc3REcml2ZScsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOihqOagvOaVsOaNrg0KICAgICAgbGlzdDogW10sDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgc2VsZWN0ZWRSb3dzOiBbXSwNCiAgICAgIC8vIOW8ueeql+ebuOWFsw0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICB0aXRsZTogJycsDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBjdXN0b21lck5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgY2FyTW9kZWxJZDogdW5kZWZpbmVkLA0KICAgICAgICByb2FkQ29uZGl0aW9uSWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgc3RhcnREYXRlOiB1bmRlZmluZWQsDQogICAgICAgIGVuZERhdGU6IHVuZGVmaW5lZA0KICAgICAgfSwNCiAgICAgIC8vIOaXtumXtOiMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOS4i+aLiemAiemhuQ0KICAgICAgY2FyTW9kZWxMaXN0OiBbXSwNCiAgICAgIHJvYWRMaXN0OiBbXQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICAgIHRoaXMubG9hZFNlbGVjdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5Yqg6L295LiL5ouJ6YCJ6aG5ICovDQogICAgbG9hZFNlbGVjdCgpIHsNCiAgICAgIGxpc3RDYXJNb2RlbCgpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5jYXJNb2RlbExpc3QgPSByZXMucm93cyB8fCBbXQ0KICAgICAgfSkNCiAgICAgIGxpc3RSb2FkQ29uZGl0aW9uKCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLnJvYWRMaXN0ID0gcmVzLnJvd3MgfHwgW10NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6LliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgLy8g5aSE55CG5pe26Ze06IyD5Zu0DQogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnREYXRlID0gdGhpcy5kYXRlUmFuZ2VbMF0NCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmREYXRlID0gdGhpcy5kYXRlUmFuZ2VbMV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnREYXRlID0gdW5kZWZpbmVkDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kRGF0ZSA9IHVuZGVmaW5lZA0KICAgICAgfQ0KDQogICAgICBsaXN0VGVzdERyaXZlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5saXN0ID0gcmVzLnJvd3MgfHwgW10NCiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbCB8fCAwDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQoNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW10NCiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgY3VzdG9tZXJOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIGNhck1vZGVsSWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgcm9hZENvbmRpdGlvbklkOiB1bmRlZmluZWQsDQogICAgICAgIHN0YXJ0RGF0ZTogdW5kZWZpbmVkLA0KICAgICAgICBlbmREYXRlOiB1bmRlZmluZWQNCiAgICAgIH0NCiAgICAgIHRoaXMuJHJlZnMucXVlcnlGb3JtLnJlc2V0RmllbGRzKCkNCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCg0KICAgIC8qKiDlpJrpgInmoYbpgInkuK3mlbDmja4gKi8NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnNlbGVjdGVkUm93cyA9IHNlbGVjdGlvbg0KICAgIH0sDQoNCiAgICAvKiog5YiG6aG155u45YWzICovDQogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZVNpemUgPSB2YWwNCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCg0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSB2YWwNCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCg0KICAgIC8qKiDojrflj5bot6/lhrXmoIfnrb7nsbvlnosgKi8NCiAgICBnZXRSb2FkQ29uZGl0aW9uVHlwZShyb2FkQ29uZGl0aW9uKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAn6auY6YCf6Lev5Ya1JzogJ3N1Y2Nlc3MnLA0KICAgICAgICAn5Z+O5biC6Lev5Ya1JzogJ3ByaW1hcnknLA0KICAgICAgICAn5bGx6Lev6Lev5Ya1JzogJ3dhcm5pbmcnLA0KICAgICAgICAn5oG25Yqj6Lev5Ya1JzogJ2RhbmdlcicNCiAgICAgIH0NCiAgICAgIHJldHVybiB0eXBlTWFwW3JvYWRDb25kaXRpb25dIHx8ICdpbmZvJw0KICAgIH0sDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5mb3JtID0ge30NCiAgICAgIHRoaXMudGl0bGUgPSAn5paw5aKe6K+V6am+6K6w5b2VJw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgaGFuZGxlRWRpdChyb3cpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH0NCiAgICAgIHRoaXMudGl0bGUgPSAn57yW6L6R6K+V6am+6K6w5b2VJw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIGNvbnN0IGFwaSA9IHRoaXMuZm9ybS5pZCA/IHVwZGF0ZVRlc3REcml2ZSA6IGFkZFRlc3REcml2ZQ0KICAgICAgYXBpKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJykNCiAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTliKDpmaTor6XorrDlvZXlkJfvvJ8nLCAn5o+Q56S6JykudGhlbigoKSA9PiB7DQogICAgICAgIGRlbFRlc3REcml2ZShyb3cuaWQpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVVwbG9hZFN1Y2Nlc3MocmVzKSB7DQogICAgICB0aGlzLmZvcm0udGVzdERhdGFVcmwgPSByZXMudXJsDQogICAgfSwNCg0KICAgIC8qKiDmibnph4/kv67mlLkgKi8NCiAgICBoYW5kbGVCYXRjaEVkaXQoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd3MubGVuZ3RoICE9PSAxKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5LiA5p2h6K6w5b2V6L+b6KGM5L+u5pS5JykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmhhbmRsZUVkaXQodGhpcy5zZWxlY3RlZFJvd3NbMF0pDQogICAgfSwNCg0KICAgIC8qKiDmibnph4/liKDpmaQgKi8NCiAgICBoYW5kbGVCYXRjaERlbGV0ZSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7foh7PlsJHpgInmi6nkuIDmnaHorrDlvZUnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgaWRzID0gdGhpcy5zZWxlY3RlZFJvd3MubWFwKHJvdyA9PiByb3cuaWQpDQogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTliKDpmaTpgInkuK3nmoQke2lkcy5sZW5ndGh95p2h6K6w5b2V5ZCX77yfYCwgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6L+Z6YeM6ZyA6KaB6LCD55So5om56YeP5Yig6Zmk55qEQVBJDQogICAgICAgIFByb21pc2UuYWxsKGlkcy5tYXAoaWQgPT4gZGVsVGVzdERyaXZlKGlkKSkpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuLA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/web/car", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询表单 -->\r\n    <div class=\"search-form-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" class=\"search-form\">\r\n        <el-form-item label=\"客户\" prop=\"customerName\">\r\n          <el-input\r\n            v-model=\"queryParams.customerName\"\r\n            placeholder=\"请输入客户名称\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\" prop=\"carModelId\">\r\n          <el-select\r\n            v-model=\"queryParams.carModelId\"\r\n            placeholder=\"请选择车型\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\" prop=\"roadConditionId\">\r\n          <el-select\r\n            v-model=\"queryParams.roadConditionId\"\r\n            placeholder=\"请选择路况\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"创建时间\" prop=\"dateRange\">\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"—\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 240px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item class=\"search-buttons\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 操作按钮 -->\r\n      <div class=\"action-buttons\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"success\" icon=\"el-icon-edit\" :disabled=\"!selectedRows.length\" @click=\"handleBatchEdit\">修改</el-button>\r\n        <el-button type=\"danger\" icon=\"el-icon-delete\" :disabled=\"!selectedRows.length\" @click=\"handleBatchDelete\">删除</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-container\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        border\r\n        stripe\r\n        @selection-change=\"handleSelectionChange\"\r\n        class=\"data-table\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"客户\" prop=\"customerName\" min-width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"车型\" prop=\"carModelName\" min-width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"路况\" prop=\"roadConditionName\" min-width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getRoadConditionType(scope.row.roadConditionName)\">\r\n              {{ scope.row.roadConditionName }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" prop=\"createdAt\" width=\"180\" align=\"center\" />\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleEdit(scope.row)\"\r\n              class=\"edit-btn\"\r\n            >\r\n              修改\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              class=\"delete-btn\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页组件 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"queryParams.pageNum\"\r\n          :page-sizes=\"[5, 10, 20, 50]\"\r\n          :page-size=\"queryParams.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'\r\nimport { listCarModel } from '@/api/web/carModel'\r\nimport { listRoadCondition } from '@/api/web/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  data() {\r\n    return {\r\n      // 表格数据\r\n      list: [],\r\n      loading: false,\r\n      total: 0,\r\n      selectedRows: [],\r\n      // 弹窗相关\r\n      open: false,\r\n      title: '',\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: undefined,\r\n        carModelId: undefined,\r\n        roadConditionId: undefined,\r\n        startDate: undefined,\r\n        endDate: undefined\r\n      },\r\n      // 时间范围\r\n      dateRange: [],\r\n      // 下拉选项\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    /** 加载下拉选项 */\r\n    loadSelect() {\r\n      listCarModel().then(res => {\r\n        this.carModelList = res.rows || []\r\n      })\r\n      listRoadCondition().then(res => {\r\n        this.roadList = res.rows || []\r\n      })\r\n    },\r\n\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true\r\n      // 处理时间范围\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        this.queryParams.startDate = this.dateRange[0]\r\n        this.queryParams.endDate = this.dateRange[1]\r\n      } else {\r\n        this.queryParams.startDate = undefined\r\n        this.queryParams.endDate = undefined\r\n      }\r\n\r\n      listTestDrive(this.queryParams).then(res => {\r\n        this.list = res.rows || []\r\n        this.total = res.total || 0\r\n        this.loading = false\r\n      }).catch(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: undefined,\r\n        carModelId: undefined,\r\n        roadConditionId: undefined,\r\n        startDate: undefined,\r\n        endDate: undefined\r\n      }\r\n      this.$refs.queryForm.resetFields()\r\n      this.getList()\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection\r\n    },\r\n\r\n    /** 分页相关 */\r\n    handleSizeChange(val) {\r\n      this.queryParams.pageSize = val\r\n      this.getList()\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.queryParams.pageNum = val\r\n      this.getList()\r\n    },\r\n\r\n    /** 获取路况标签类型 */\r\n    getRoadConditionType(roadCondition) {\r\n      const typeMap = {\r\n        '高速路况': 'success',\r\n        '城市路况': 'primary',\r\n        '山路路况': 'warning',\r\n        '恶劣路况': 'danger'\r\n      }\r\n      return typeMap[roadCondition] || 'info'\r\n    },\r\n    handleAdd() {\r\n      this.form = {}\r\n      this.title = '新增试驾记录'\r\n      this.open = true\r\n    },\r\n    handleEdit(row) {\r\n      this.form = { ...row }\r\n      this.title = '编辑试驾记录'\r\n      this.open = true\r\n    },\r\n    submitForm() {\r\n      const api = this.form.id ? updateTestDrive : addTestDrive\r\n      api(this.form).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该记录吗？', '提示').then(() => {\r\n        delTestDrive(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    },\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url\r\n    },\r\n\r\n    /** 批量修改 */\r\n    handleBatchEdit() {\r\n      if (this.selectedRows.length !== 1) {\r\n        this.$message.warning('请选择一条记录进行修改')\r\n        return\r\n      }\r\n      this.handleEdit(this.selectedRows[0])\r\n    },\r\n\r\n    /** 批量删除 */\r\n    handleBatchDelete() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请至少选择一条记录')\r\n        return\r\n      }\r\n\r\n      const ids = this.selectedRows.map(row => row.id)\r\n      this.$confirm(`确认删除选中的${ids.length}条记录吗？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 这里需要调用批量删除的API\r\n        Promise.all(ids.map(id => delTestDrive(id))).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 搜索表单样式 */\r\n.search-form-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n\r\n  .search-form {\r\n    .el-form-item {\r\n      margin-bottom: 16px;\r\n      margin-right: 20px;\r\n\r\n      .el-form-item__label {\r\n        font-weight: 500;\r\n        color: #606266;\r\n      }\r\n    }\r\n\r\n    .search-buttons {\r\n      .el-button {\r\n        margin-left: 10px;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    margin-top: 16px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #ebeef5;\r\n\r\n    .el-button {\r\n      margin-right: 10px;\r\n\r\n      &.el-button--primary {\r\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--success {\r\n        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--danger {\r\n        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);\r\n        border: none;\r\n      }\r\n\r\n      &:hover {\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n\r\n  .data-table {\r\n    border: none;\r\n\r\n    ::v-deep .el-table__header-wrapper {\r\n      .el-table__header {\r\n        th {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          font-weight: 600;\r\n          border: none;\r\n\r\n          .cell {\r\n            padding: 12px 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    ::v-deep .el-table__body-wrapper {\r\n      .el-table__body {\r\n        tr {\r\n          &:hover {\r\n            background-color: #f8f9ff;\r\n          }\r\n\r\n          td {\r\n            border-bottom: 1px solid #f0f2f5;\r\n\r\n            .cell {\r\n              padding: 12px 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .edit-btn {\r\n      color: #409eff;\r\n\r\n      &:hover {\r\n        color: #66b1ff;\r\n        background-color: #ecf5ff;\r\n      }\r\n    }\r\n\r\n    .delete-btn {\r\n      color: #f56c6c;\r\n\r\n      &:hover {\r\n        color: #f78989;\r\n        background-color: #fef0f0;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 分页样式 */\r\n  .pagination-container {\r\n    padding: 20px;\r\n    text-align: right;\r\n    border-top: 1px solid #f0f2f5;\r\n    background-color: #fafbfc;\r\n\r\n    ::v-deep .el-pagination {\r\n      .el-pager li {\r\n        &.active {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          border-radius: 4px;\r\n        }\r\n\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n\r\n      .btn-prev,\r\n      .btn-next {\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form-container {\r\n    .search-form {\r\n      .el-form-item {\r\n        margin-right: 0;\r\n        width: 100%;\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-date-picker {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .action-buttons {\r\n      .el-button {\r\n        margin-bottom: 10px;\r\n        width: 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .table-container {\r\n    .data-table {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .pagination-container {\r\n      text-align: center;\r\n\r\n      ::v-deep .el-pagination {\r\n        .el-pagination__sizes,\r\n        .el-pagination__jump {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}