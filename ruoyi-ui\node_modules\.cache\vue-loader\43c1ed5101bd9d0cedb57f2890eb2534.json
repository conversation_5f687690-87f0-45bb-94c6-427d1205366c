{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765866230901}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/web/car", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询 -->\r\n    <el-form :model=\"queryParams\" inline>\r\n      <el-form-item label=\"客户\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"客户名称\"/>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"车型\">\r\n        <el-select v-model=\"queryParams.carModelId\" clearable>\r\n          <el-option\r\n            v-for=\"item in carModelList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.modelName\"\r\n            :value=\"item.id\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"路况\">\r\n        <el-select v-model=\"queryParams.roadConditionId\" clearable>\r\n          <el-option\r\n            v-for=\"item in roadList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.conditionName\"\r\n            :value=\"item.id\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-button type=\"primary\" @click=\"getList\">搜索</el-button>\r\n      <el-button type=\"success\" @click=\"handleAdd\">新增</el-button>\r\n    </el-form>\r\n\r\n    <!-- 表格 -->\r\n    <el-table :data=\"list\" border style=\"margin-top: 20px\">\r\n      <el-table-column label=\"客户\" prop=\"customerName\"/>\r\n      <el-table-column label=\"车型\" prop=\"carModelName\"/>\r\n      <el-table-column label=\"路况\" prop=\"roadConditionName\"/>\r\n      <el-table-column label=\"试驾数据\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link v-if=\"scope.row.testDataUrl\"\r\n                   :href=\"scope.row.testDataUrl\"\r\n                   target=\"_blank\">查看</el-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" prop=\"createdAt\"/>\r\n      <el-table-column label=\"操作\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/system/testDrive'\r\nimport { listCarModel } from '@/api/system/carModel'\r\nimport { listRoadCondition } from '@/api/system/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  data() {\r\n    return {\r\n      list: [],\r\n      open: false,\r\n      title: '',\r\n      form: {},\r\n      queryParams: {},\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    loadSelect() {\r\n      listCarModel().then(res => this.carModelList = res.rows)\r\n      listRoadCondition().then(res => this.roadList = res.rows)\r\n    },\r\n    getList() {\r\n      listTestDrive(this.queryParams).then(res => {\r\n        this.list = res.rows\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.form = {}\r\n      this.title = '新增试驾记录'\r\n      this.open = true\r\n    },\r\n    handleEdit(row) {\r\n      this.form = { ...row }\r\n      this.title = '编辑试驾记录'\r\n      this.open = true\r\n    },\r\n    submitForm() {\r\n      const api = this.form.id ? updateTestDrive : addTestDrive\r\n      api(this.form).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该记录吗？', '提示').then(() => {\r\n        delTestDrive(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    },\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}