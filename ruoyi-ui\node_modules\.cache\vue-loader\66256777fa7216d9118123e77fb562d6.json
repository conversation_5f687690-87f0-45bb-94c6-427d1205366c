{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\roadCondition\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\roadCondition\\index.vue", "mtime": 1765866208480}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0Um9hZENvbmRpdGlvbiwNCiAgYWRkUm9hZENvbmRpdGlvbiwNCiAgdXBkYXRlUm9hZENvbmRpdGlvbiwNCiAgZGVsUm9hZENvbmRpdGlvbg0KfSBmcm9tICdAL2FwaS9zeXN0ZW0vcm9hZENvbmRpdGlvbicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUm9hZENvbmRpdGlvbicsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxpc3Q6IFtdLA0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICB0aXRsZTogJycsDQogICAgICBmb3JtOiB7fQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIGxpc3RSb2FkQ29uZGl0aW9uKCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmxpc3QgPSByZXMucm93cw0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHt9DQogICAgICB0aGlzLnRpdGxlID0gJ+aWsOWinui3r+WGtScNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZUVkaXQocm93KSB7DQogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9DQogICAgICB0aGlzLnRpdGxlID0gJ+e8lui+kei3r+WGtScNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICB9LA0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICBjb25zdCBhcGkgPSB0aGlzLmZvcm0uaWQgPyB1cGRhdGVSb2FkQ29uZGl0aW9uIDogYWRkUm9hZENvbmRpdGlvbg0KICAgICAgYXBpKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJykNCiAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTliKDpmaTor6Xot6/lhrXlkJfvvJ8nLCAn5o+Q56S6JykudGhlbigoKSA9PiB7DQogICAgICAgIGRlbFJvYWRDb25kaXRpb24ocm93LmlkKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/web/roadCondition", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">\r\n      新增\r\n    </el-button>\r\n\r\n    <el-table :data=\"list\" border style=\"margin-top: 20px\">\r\n      <el-table-column label=\"ID\" prop=\"id\" width=\"80\"/>\r\n      <el-table-column label=\"路况名称\" prop=\"conditionName\"/>\r\n      <el-table-column label=\"描述\" prop=\"description\"/>\r\n      <el-table-column label=\"操作\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"450px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"路况名称\">\r\n          <el-input v-model=\"form.conditionName\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"描述\">\r\n          <el-input type=\"textarea\" v-model=\"form.description\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listRoadCondition,\r\n  addRoadCondition,\r\n  updateRoadCondition,\r\n  delRoadCondition\r\n} from '@/api/system/roadCondition'\r\n\r\nexport default {\r\n  name: 'RoadCondition',\r\n  data() {\r\n    return {\r\n      list: [],\r\n      open: false,\r\n      title: '',\r\n      form: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      listRoadCondition().then(res => {\r\n        this.list = res.rows\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.form = {}\r\n      this.title = '新增路况'\r\n      this.open = true\r\n    },\r\n    handleEdit(row) {\r\n      this.form = { ...row }\r\n      this.title = '编辑路况'\r\n      this.open = true\r\n    },\r\n    submitForm() {\r\n      const api = this.form.id ? updateRoadCondition : addRoadCondition\r\n      api(this.form).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该路况吗？', '提示').then(() => {\r\n        delRoadCondition(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}