{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\IconSelect\\index.vue?vue&type=style&index=0&id=6504d548&rel=stylesheet%2Fscss&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\components\\IconSelect\\index.vue", "mtime": 1739929493644}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756259104758}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756259107479}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756259105037}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1756259105524}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmljb24tYm9keSB7CiAgd2lkdGg6IDEwMCU7CiAgcGFkZGluZzogMTBweDsKICAuaWNvbi1zZWFyY2ggewogICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgbWFyZ2luLWJvdHRvbTogNXB4OwogIH0KICAuaWNvbi1saXN0IHsKICAgIGhlaWdodDogMjAwcHg7CiAgICBvdmVyZmxvdzogYXV0bzsKICAgIC5saXN0LWNvbnRhaW5lciB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtd3JhcDogd3JhcDsKICAgICAgLmljb24taXRlbS13cmFwcGVyIHsKICAgICAgICB3aWR0aDogY2FsYygxMDAlIC8gMyk7CiAgICAgICAgaGVpZ2h0OiAyNXB4OwogICAgICAgIGxpbmUtaGVpZ2h0OiAyNXB4OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIC5pY29uLWl0ZW0gewogICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgIG1heC13aWR0aDogMTAwJTsKICAgICAgICAgIGhlaWdodDogMTAwJTsKICAgICAgICAgIHBhZGRpbmc6IDAgNXB4OwogICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlY2VjZWM7CiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsKICAgICAgICAgIH0KICAgICAgICAgIC5pY29uIHsKICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7CiAgICAgICAgICB9CiAgICAgICAgICBzcGFuIHsKICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgICAgICB2ZXJ0aWNhbC1hbGlnbjogLTAuMTVlbTsKICAgICAgICAgICAgZmlsbDogY3VycmVudENvbG9yOwogICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDJweDsKICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIC5pY29uLWl0ZW0uYWN0aXZlIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNlY2VjZWM7CiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1cHg7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\r\n<template>\r\n  <div class=\"icon-body\">\r\n    <el-input v-model=\"name\" class=\"icon-search\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input=\"filterIcons\">\r\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\r\n    </el-input>\r\n    <div class=\"icon-list\">\r\n      <div class=\"list-container\">\r\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\r\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\r\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\r\n            <span>{{ item }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport icons from './requireIcons'\r\nexport default {\r\n  name: 'IconSelect',\r\n  props: {\r\n    activeIcon: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      name: '',\r\n      iconList: icons\r\n    }\r\n  },\r\n  methods: {\r\n    filterIcons() {\r\n      this.iconList = icons\r\n      if (this.name) {\r\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\r\n      }\r\n    },\r\n    selectedIcon(name) {\r\n      this.$emit('selected', name)\r\n      document.body.click()\r\n    },\r\n    reset() {\r\n      this.name = ''\r\n      this.iconList = icons\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n  .icon-body {\r\n    width: 100%;\r\n    padding: 10px;\r\n    .icon-search {\r\n      position: relative;\r\n      margin-bottom: 5px;\r\n    }\r\n    .icon-list {\r\n      height: 200px;\r\n      overflow: auto;\r\n      .list-container {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .icon-item-wrapper {\r\n          width: calc(100% / 3);\r\n          height: 25px;\r\n          line-height: 25px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          .icon-item {\r\n            display: flex;\r\n            max-width: 100%;\r\n            height: 100%;\r\n            padding: 0 5px;\r\n            &:hover {\r\n              background: #ececec;\r\n              border-radius: 5px;\r\n            }\r\n            .icon {\r\n              flex-shrink: 0;\r\n            }\r\n            span {\r\n              display: inline-block;\r\n              vertical-align: -0.15em;\r\n              fill: currentColor;\r\n              padding-left: 2px;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n          }\r\n          .icon-item.active {\r\n            background: #ececec;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}