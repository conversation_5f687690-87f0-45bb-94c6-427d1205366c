{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=style&index=0&id=01917032&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765867097682}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756259104758}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756259107479}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756259105037}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1756259105524}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2UA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/web/car", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"请输入客户名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"车型\" prop=\"carModelId\">\r\n        <el-select v-model=\"queryParams.carModelId\" placeholder=\"请选择车型\" clearable>\r\n          <el-option\r\n            v-for=\"item in carModelList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.modelName\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"路况\" prop=\"roadConditionId\">\r\n        <el-select v-model=\"queryParams.roadConditionId\" placeholder=\"请选择路况\" clearable>\r\n          <el-option\r\n            v-for=\"item in roadList\"\r\n            :key=\"item.id\"\r\n            :label=\"item.conditionName\"\r\n            :value=\"item.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          size=\"small\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"客户\" align=\"center\" prop=\"customerName\" />\r\n      <el-table-column label=\"车型\" align=\"center\" prop=\"carModelName\" />\r\n      <el-table-column label=\"路况\" align=\"center\" prop=\"roadConditionName\" />\r\n      <el-table-column label=\"试驾数据\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link v-if=\"scope.row.testDataUrl\"\r\n                   :href=\"scope.row.testDataUrl\"\r\n                   target=\"_blank\">查看</el-link>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'\r\nimport { listCarModel } from '@/api/web/carModel'\r\nimport { listRoadCondition } from '@/api/web/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 试驾记录表格数据\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: null,\r\n        carModelId: null,\r\n        roadConditionId: null,\r\n      },\r\n      // 时间范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 下拉选项\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    /** 查询试驾记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      this.queryParams.params = {};\r\n      if (null != this.dateRange && '' != this.dateRange) {\r\n        this.queryParams.params[\"beginTime\"] = this.dateRange[0];\r\n        this.queryParams.params[\"endTime\"] = this.dateRange[1];\r\n      }\r\n      listTestDrive(this.queryParams).then(response => {\r\n        this.list = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        customerName: null,\r\n        carModelId: null,\r\n        roadConditionId: null,\r\n        testDataUrl: null,\r\n        createdAt: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加试驾记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.form = { ...row };\r\n      this.open = true;\r\n      this.title = \"修改试驾记录\";\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateTestDrive(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTestDrive(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除试驾记录编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delTestDrive(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 上传成功回调 */\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url;\r\n    },\r\n    /** 加载下拉选项 */\r\n    loadSelect() {\r\n      listCarModel().then(res => {\r\n        this.carModelList = res.rows || []\r\n      })\r\n      listRoadCondition().then(res => {\r\n        this.roadList = res.rows || []\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 搜索表单样式 */\r\n.search-form-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n\r\n  .search-form {\r\n    .el-form-item {\r\n      margin-bottom: 16px;\r\n      margin-right: 20px;\r\n\r\n      .el-form-item__label {\r\n        font-weight: 500;\r\n        color: #606266;\r\n      }\r\n    }\r\n\r\n    .search-buttons {\r\n      .el-button {\r\n        margin-left: 10px;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    margin-top: 16px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #ebeef5;\r\n\r\n    .el-button {\r\n      margin-right: 10px;\r\n\r\n      &.el-button--primary {\r\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--success {\r\n        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--danger {\r\n        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);\r\n        border: none;\r\n      }\r\n\r\n      &:hover {\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n\r\n  .data-table {\r\n    border: none;\r\n\r\n    ::v-deep .el-table__header-wrapper {\r\n      .el-table__header {\r\n        th {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          font-weight: 600;\r\n          border: none;\r\n\r\n          .cell {\r\n            padding: 12px 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    ::v-deep .el-table__body-wrapper {\r\n      .el-table__body {\r\n        tr {\r\n          &:hover {\r\n            background-color: #f8f9ff;\r\n          }\r\n\r\n          td {\r\n            border-bottom: 1px solid #f0f2f5;\r\n\r\n            .cell {\r\n              padding: 12px 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .edit-btn {\r\n      color: #409eff;\r\n\r\n      &:hover {\r\n        color: #66b1ff;\r\n        background-color: #ecf5ff;\r\n      }\r\n    }\r\n\r\n    .delete-btn {\r\n      color: #f56c6c;\r\n\r\n      &:hover {\r\n        color: #f78989;\r\n        background-color: #fef0f0;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 分页样式 */\r\n  .pagination-container {\r\n    padding: 20px;\r\n    text-align: right;\r\n    border-top: 1px solid #f0f2f5;\r\n    background-color: #fafbfc;\r\n\r\n    ::v-deep .el-pagination {\r\n      .el-pager li {\r\n        &.active {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          border-radius: 4px;\r\n        }\r\n\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n\r\n      .btn-prev,\r\n      .btn-next {\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form-container {\r\n    .search-form {\r\n      .el-form-item {\r\n        margin-right: 0;\r\n        width: 100%;\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-date-picker {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .action-buttons {\r\n      .el-button {\r\n        margin-bottom: 10px;\r\n        width: 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .table-container {\r\n    .data-table {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .pagination-container {\r\n      text-align: center;\r\n\r\n      ::v-deep .el-pagination {\r\n        .el-pagination__sizes,\r\n        .el-pagination__jump {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}