{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue?vue&type=style&index=0&id=01917032&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\car\\index.vue", "mtime": 1765866862858}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756259104758}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756259107479}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756259105037}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1756259105524}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6WA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/web/car", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询表单 -->\r\n    <div class=\"search-form-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" class=\"search-form\">\r\n        <el-form-item label=\"客户\" prop=\"customerName\">\r\n          <el-input\r\n            v-model=\"queryParams.customerName\"\r\n            placeholder=\"请输入客户名称\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\" prop=\"carModelId\">\r\n          <el-select\r\n            v-model=\"queryParams.carModelId\"\r\n            placeholder=\"请选择车型\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\" prop=\"roadConditionId\">\r\n          <el-select\r\n            v-model=\"queryParams.roadConditionId\"\r\n            placeholder=\"请选择路况\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"创建时间\" prop=\"dateRange\">\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"—\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 240px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item class=\"search-buttons\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 操作按钮 -->\r\n      <div class=\"action-buttons\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"success\" icon=\"el-icon-edit\" :disabled=\"!selectedRows.length\" @click=\"handleBatchEdit\">修改</el-button>\r\n        <el-button type=\"danger\" icon=\"el-icon-delete\" :disabled=\"!selectedRows.length\" @click=\"handleBatchDelete\">删除</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-container\">\r\n      <el-table\r\n        :data=\"list\"\r\n        v-loading=\"loading\"\r\n        border\r\n        stripe\r\n        @selection-change=\"handleSelectionChange\"\r\n        class=\"data-table\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"客户\" prop=\"customerName\" min-width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"车型\" prop=\"carModelName\" min-width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"路况\" prop=\"roadConditionName\" min-width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getRoadConditionType(scope.row.roadConditionName)\">\r\n              {{ scope.row.roadConditionName }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" prop=\"createdAt\" width=\"180\" align=\"center\" />\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleEdit(scope.row)\"\r\n              class=\"edit-btn\"\r\n            >\r\n              修改\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              class=\"delete-btn\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页组件 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"queryParams.pageNum\"\r\n          :page-sizes=\"[5, 10, 20, 50]\"\r\n          :page-size=\"queryParams.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"客户\">\r\n          <el-input v-model=\"form.customerName\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车型\">\r\n          <el-select v-model=\"form.carModelId\">\r\n            <el-option\r\n              v-for=\"item in carModelList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.modelName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"路况\">\r\n          <el-select v-model=\"form.roadConditionId\">\r\n            <el-option\r\n              v-for=\"item in roadList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.conditionName\"\r\n              :value=\"item.id\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"试驾数据\">\r\n          <el-upload\r\n            action=\"/common/upload\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :show-file-list=\"false\">\r\n            <el-button type=\"primary\">上传文件</el-button>\r\n          </el-upload>\r\n          <div v-if=\"form.testDataUrl\">{{ form.testDataUrl }}</div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'\r\nimport { listCarModel } from '@/api/web/carModel'\r\nimport { listRoadCondition } from '@/api/web/roadCondition'\r\n\r\nexport default {\r\n  name: 'TestDrive',\r\n  data() {\r\n    return {\r\n      // 表格数据\r\n      list: [],\r\n      loading: false,\r\n      total: 0,\r\n      selectedRows: [],\r\n      // 弹窗相关\r\n      open: false,\r\n      title: '',\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: undefined,\r\n        carModelId: undefined,\r\n        roadConditionId: undefined,\r\n        startDate: undefined,\r\n        endDate: undefined\r\n      },\r\n      // 时间范围\r\n      dateRange: [],\r\n      // 下拉选项\r\n      carModelList: [],\r\n      roadList: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSelect()\r\n  },\r\n  methods: {\r\n    /** 加载下拉选项 */\r\n    loadSelect() {\r\n      listCarModel().then(res => {\r\n        this.carModelList = res.rows || []\r\n      })\r\n      listRoadCondition().then(res => {\r\n        this.roadList = res.rows || []\r\n      })\r\n    },\r\n\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true\r\n      // 处理时间范围\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        this.queryParams.startDate = this.dateRange[0]\r\n        this.queryParams.endDate = this.dateRange[1]\r\n      } else {\r\n        this.queryParams.startDate = undefined\r\n        this.queryParams.endDate = undefined\r\n      }\r\n\r\n      listTestDrive(this.queryParams).then(res => {\r\n        this.list = res.rows || []\r\n        this.total = res.total || 0\r\n        this.loading = false\r\n      }).catch(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        customerName: undefined,\r\n        carModelId: undefined,\r\n        roadConditionId: undefined,\r\n        startDate: undefined,\r\n        endDate: undefined\r\n      }\r\n      this.$refs.queryForm.resetFields()\r\n      this.getList()\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection\r\n    },\r\n\r\n    /** 分页相关 */\r\n    handleSizeChange(val) {\r\n      this.queryParams.pageSize = val\r\n      this.getList()\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.queryParams.pageNum = val\r\n      this.getList()\r\n    },\r\n\r\n    /** 获取路况标签类型 */\r\n    getRoadConditionType(roadCondition) {\r\n      const typeMap = {\r\n        '高速路况': 'success',\r\n        '城市路况': 'primary',\r\n        '山路路况': 'warning',\r\n        '恶劣路况': 'danger'\r\n      }\r\n      return typeMap[roadCondition] || 'info'\r\n    },\r\n    handleAdd() {\r\n      this.form = {}\r\n      this.title = '新增试驾记录'\r\n      this.open = true\r\n    },\r\n    handleEdit(row) {\r\n      this.form = { ...row }\r\n      this.title = '编辑试驾记录'\r\n      this.open = true\r\n    },\r\n    submitForm() {\r\n      const api = this.form.id ? updateTestDrive : addTestDrive\r\n      api(this.form).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该记录吗？', '提示').then(() => {\r\n        delTestDrive(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    },\r\n    handleUploadSuccess(res) {\r\n      this.form.testDataUrl = res.url\r\n    },\r\n\r\n    /** 批量修改 */\r\n    handleBatchEdit() {\r\n      if (this.selectedRows.length !== 1) {\r\n        this.$message.warning('请选择一条记录进行修改')\r\n        return\r\n      }\r\n      this.handleEdit(this.selectedRows[0])\r\n    },\r\n\r\n    /** 批量删除 */\r\n    handleBatchDelete() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$message.warning('请至少选择一条记录')\r\n        return\r\n      }\r\n\r\n      const ids = this.selectedRows.map(row => row.id)\r\n      this.$confirm(`确认删除选中的${ids.length}条记录吗？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 这里需要调用批量删除的API\r\n        Promise.all(ids.map(id => delTestDrive(id))).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n}\r\n\r\n/* 搜索表单样式 */\r\n.search-form-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n\r\n  .search-form {\r\n    .el-form-item {\r\n      margin-bottom: 16px;\r\n      margin-right: 20px;\r\n\r\n      .el-form-item__label {\r\n        font-weight: 500;\r\n        color: #606266;\r\n      }\r\n    }\r\n\r\n    .search-buttons {\r\n      .el-button {\r\n        margin-left: 10px;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    margin-top: 16px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid #ebeef5;\r\n\r\n    .el-button {\r\n      margin-right: 10px;\r\n\r\n      &.el-button--primary {\r\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--success {\r\n        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);\r\n        border: none;\r\n      }\r\n\r\n      &.el-button--danger {\r\n        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);\r\n        border: none;\r\n      }\r\n\r\n      &:hover {\r\n        transform: translateY(-1px);\r\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n\r\n  .data-table {\r\n    border: none;\r\n\r\n    ::v-deep .el-table__header-wrapper {\r\n      .el-table__header {\r\n        th {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          font-weight: 600;\r\n          border: none;\r\n\r\n          .cell {\r\n            padding: 12px 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    ::v-deep .el-table__body-wrapper {\r\n      .el-table__body {\r\n        tr {\r\n          &:hover {\r\n            background-color: #f8f9ff;\r\n          }\r\n\r\n          td {\r\n            border-bottom: 1px solid #f0f2f5;\r\n\r\n            .cell {\r\n              padding: 12px 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .edit-btn {\r\n      color: #409eff;\r\n\r\n      &:hover {\r\n        color: #66b1ff;\r\n        background-color: #ecf5ff;\r\n      }\r\n    }\r\n\r\n    .delete-btn {\r\n      color: #f56c6c;\r\n\r\n      &:hover {\r\n        color: #f78989;\r\n        background-color: #fef0f0;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 分页样式 */\r\n  .pagination-container {\r\n    padding: 20px;\r\n    text-align: right;\r\n    border-top: 1px solid #f0f2f5;\r\n    background-color: #fafbfc;\r\n\r\n    ::v-deep .el-pagination {\r\n      .el-pager li {\r\n        &.active {\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          color: #fff;\r\n          border-radius: 4px;\r\n        }\r\n\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n\r\n      .btn-prev,\r\n      .btn-next {\r\n        &:hover {\r\n          color: #667eea;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form-container {\r\n    .search-form {\r\n      .el-form-item {\r\n        margin-right: 0;\r\n        width: 100%;\r\n\r\n        .el-input,\r\n        .el-select,\r\n        .el-date-picker {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .action-buttons {\r\n      .el-button {\r\n        margin-bottom: 10px;\r\n        width: 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .table-container {\r\n    .data-table {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .pagination-container {\r\n      text-align: center;\r\n\r\n      ::v-deep .el-pagination {\r\n        .el-pagination__sizes,\r\n        .el-pagination__jump {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}