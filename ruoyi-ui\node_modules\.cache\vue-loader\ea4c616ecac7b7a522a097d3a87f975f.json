{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\carModel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\src\\views\\web\\carModel\\index.vue", "mtime": 1765866212731}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756259106192}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756259105419}, {"path": "C:\\Users\\<USER>\\Desktop\\浩太\\官网\\baoneng\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756259105801}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0Q2FyTW9kZWwsDQogIGFkZENhck1vZGVsLA0KICB1cGRhdGVDYXJNb2RlbCwNCiAgZGVsQ2FyTW9kZWwNCn0gZnJvbSAnQC9hcGkvd2ViL2Nhck1vZGVsJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdDYXJNb2RlbCcsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxpc3Q6IFtdLA0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICB0aXRsZTogJycsDQogICAgICBmb3JtOiB7fQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIGxpc3RDYXJNb2RlbCgpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5saXN0ID0gcmVzLnJvd3MNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7fQ0KICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop7ovablnosnDQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfQ0KICAgICAgdGhpcy50aXRsZSA9ICfnvJbovpHovablnosnDQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgfSwNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgY29uc3QgYXBpID0gdGhpcy5mb3JtLmlkID8gdXBkYXRlQ2FyTW9kZWwgOiBhZGRDYXJNb2RlbA0KICAgICAgYXBpKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJykNCiAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTliKDpmaTor6XovablnovlkJfvvJ8nLCAn5o+Q56S6JykudGhlbigoKSA9PiB7DQogICAgICAgIGRlbENhck1vZGVsKHJvdy5pZCkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/web/carModel", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">\r\n      新增\r\n    </el-button>\r\n\r\n    <el-table :data=\"list\" style=\"margin-top: 20px\" border>\r\n      <el-table-column label=\"ID\" prop=\"id\" width=\"80\"/>\r\n      <el-table-column label=\"车型名称\" prop=\"modelName\"/>\r\n      <el-table-column label=\"车型编码\" prop=\"modelCode\"/>\r\n      <el-table-column label=\"操作\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 弹窗 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"400px\">\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"车型名称\">\r\n          <el-input v-model=\"form.modelName\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"车型编码\">\r\n          <el-input v-model=\"form.modelCode\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"open=false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listCarModel,\r\n  addCarModel,\r\n  updateCarModel,\r\n  delCarModel\r\n} from '@/api/web/carModel'\r\n\r\nexport default {\r\n  name: 'CarModel',\r\n  data() {\r\n    return {\r\n      list: [],\r\n      open: false,\r\n      title: '',\r\n      form: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getList() {\r\n      listCarModel().then(res => {\r\n        this.list = res.rows\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.form = {}\r\n      this.title = '新增车型'\r\n      this.open = true\r\n    },\r\n    handleEdit(row) {\r\n      this.form = { ...row }\r\n      this.title = '编辑车型'\r\n      this.open = true\r\n    },\r\n    submitForm() {\r\n      const api = this.form.id ? updateCarModel : addCarModel\r\n      api(this.form).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认删除该车型吗？', '提示').then(() => {\r\n        delCarModel(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}