import request from '@/utils/request'

export function listCarModel() {
  return request({ url: '/system/carModel/list', method: 'get' })
}
export function addCarModel(data) {
  return request({ url: '/system/carModel', method: 'post', data })
}
export function updateCarModel(data) {
  return request({ url: '/system/carModel', method: 'put', data })
}
export function delCarModel(id) {
  return request({ url: '/system/carModel/' + id, method: 'delete' })
}
