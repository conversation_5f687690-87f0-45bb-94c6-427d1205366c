import request from '@/utils/request'

export function listRoadCondition() {
  return request({ url: '/system/roadCondition/list', method: 'get' })
}
export function addRoadCondition(data) {
  return request({ url: '/system/roadCondition', method: 'post', data })
}
export function updateRoadCondition(data) {
  return request({ url: '/system/roadCondition', method: 'put', data })
}
export function delRoadCondition(id) {
  return request({ url: '/system/roadCondition/' + id, method: 'delete' })
}
