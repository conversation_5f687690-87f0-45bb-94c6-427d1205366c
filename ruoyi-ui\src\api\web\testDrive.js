import request from '@/utils/request'

export function listTestDrive(query) {
  return request({ url: '/system/testDrive/list', method: 'get', params: query })
}
export function addTestDrive(data) {
  return request({ url: '/system/testDrive', method: 'post', data })
}
export function updateTestDrive(data) {
  return request({ url: '/system/testDrive', method: 'put', data })
}
export function delTestDrive(id) {
  return request({ url: '/system/testDrive/' + id, method: 'delete' })
}
