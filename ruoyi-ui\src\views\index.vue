<!-- <template>
  <div class="statistics">
    <div class="counts">
      <div class="count-box">
        <span class="label">用户总数:</span>
        <span class="value">{{ userCount }}</span>
      </div>
      <div class="count-box">
        <span class="label">今日打开总数:</span>
        <span class="value">{{ openCount }}</span>
      </div>
    </div>

    <div class="chart-box">
      <div ref="lineChart" style="width: 100%; height: 400px;"></div>
    </div>
  </div>
</template>

<script>

import * as echarts from 'echarts';
import {homeInfo, list} from "@/api/maths/exhibition";

export default {
  data() {
    return {
      userCount: 0,
      openCount: 0,
      openCountList: []
    };
  },
  methods: {
    // 请求接口数据
    fetchData() {
      homeInfo(null).then(response => {
        console.log(response)
        this.openCountList = response.data.openCountList;
        this.userCount = response.data.userCount;
        this.openCount = response.data.openCount;
        this.renderChart();
      });
    },
    // 渲染折线图
    renderChart() {
      const chart = echarts.init(this.$refs.lineChart);
      const hours = this.openCountList.map(item => item.hours);
      const counts = this.openCountList.map(item => parseInt(item.counts, 10));

      const option = {
        title: {
          text: '24小时打开频率分布'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: hours,
          axisLabel: {
            interval: 0
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: counts,
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 2
            }
          }
        ]
      };
      chart.setOption(option);
    }
  },
  mounted() {
    this.fetchData();
  }
};
</script>

<style scoped>
.statistics {
  padding: 20px;
}

.counts {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.count-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 8px;
  width: 48%;
}

.label {
  font-size: 16px;
  color: #333;
}

.value {
  font-size: 20px;
  font-weight: bold;
  color: #4CAF50;
}

.chart-box {
  margin-top: 20px;
}
</style> -->
