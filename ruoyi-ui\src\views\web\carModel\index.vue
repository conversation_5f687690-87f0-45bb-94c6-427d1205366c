<template>
  <div class="app-container">
    <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
      新增
    </el-button>

    <el-table :data="list" style="margin-top: 20px" border>
      <el-table-column label="ID" prop="id" width="80"/>
      <el-table-column label="车型名称" prop="modelName"/>
      <el-table-column label="车型编码" prop="modelCode"/>
      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="400px">
      <el-form :model="form" label-width="80px">
        <el-form-item label="车型名称">
          <el-input v-model="form.modelName"/>
        </el-form-item>
        <el-form-item label="车型编码">
          <el-input v-model="form.modelCode"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="open=false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCarModel,
  addCarModel,
  updateCarModel,
  delCarModel
} from '@/api/web/carModel'

export default {
  name: 'CarModel',
  data() {
    return {
      list: [],
      open: false,
      title: '',
      form: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      listCarModel().then(res => {
        this.list = res.rows
      })
    },
    handleAdd() {
      this.form = {}
      this.title = '新增车型'
      this.open = true
    },
    handleEdit(row) {
      this.form = { ...row }
      this.title = '编辑车型'
      this.open = true
    },
    submitForm() {
      const api = this.form.id ? updateCarModel : addCarModel
      api(this.form).then(() => {
        this.$message.success('保存成功')
        this.open = false
        this.getList()
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该车型吗？', '提示').then(() => {
        delCarModel(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    }
  }
}
</script>
