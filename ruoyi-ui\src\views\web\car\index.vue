<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车型" prop="carModelId">
        <el-select v-model="queryParams.carModelId" placeholder="请选择车型" clearable>
          <el-option
            v-for="item in carModelList"
            :key="item.id"
            :label="item.modelName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="路况" prop="roadConditionId">
        <el-select v-model="queryParams.roadConditionId" placeholder="请选择路况" clearable>
          <el-option
            v-for="item in roadList"
            :key="item.id"
            :label="item.conditionName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="客户" align="center" prop="customerName" />
      <el-table-column label="车型" align="center" prop="carModelName" />
      <el-table-column label="路况" align="center" prop="roadConditionName" />
      <el-table-column label="试驾数据" align="center">
        <template slot-scope="scope">
          <el-link v-if="scope.row.testDataUrl"
                   :href="scope.row.testDataUrl"
                   target="_blank">查看</el-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="500px">
      <el-form :model="form" label-width="80px">
        <el-form-item label="客户">
          <el-input v-model="form.customerName"/>
        </el-form-item>

        <el-form-item label="车型">
          <el-select v-model="form.carModelId">
            <el-option
              v-for="item in carModelList"
              :key="item.id"
              :label="item.modelName"
              :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-form-item label="路况">
          <el-select v-model="form.roadConditionId">
            <el-option
              v-for="item in roadList"
              :key="item.id"
              :label="item.conditionName"
              :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-form-item label="试驾数据">
          <el-upload
            action="/common/upload"
            :on-success="handleUploadSuccess"
            :show-file-list="false">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
          <div v-if="form.testDataUrl">{{ form.testDataUrl }}</div>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="open=false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'
import { listCarModel } from '@/api/web/carModel'
import { listRoadCondition } from '@/api/web/roadCondition'

export default {
  name: 'TestDrive',
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 试驾记录表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        carModelId: null,
        roadConditionId: null,
      },
      // 时间范围
      dateRange: [],
      // 表单参数
      form: {},
      // 下拉选项
      carModelList: [],
      roadList: []
    }
  },
  created() {
    this.getList()
    this.loadSelect()
  },
  methods: {
    /** 查询试驾记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.dateRange && '' != this.dateRange) {
        this.queryParams.params["beginTime"] = this.dateRange[0];
        this.queryParams.params["endTime"] = this.dateRange[1];
      }
      listTestDrive(this.queryParams).then(response => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        customerName: null,
        carModelId: null,
        roadConditionId: null,
        testDataUrl: null,
        createdAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加试驾记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = { ...row };
      this.open = true;
      this.title = "修改试驾记录";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTestDrive(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTestDrive(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除试驾记录编号为"' + ids + '"的数据项？').then(function() {
        return delTestDrive(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 上传成功回调 */
    handleUploadSuccess(res) {
      this.form.testDataUrl = res.url;
    },
    /** 加载下拉选项 */
    loadSelect() {
      listCarModel().then(res => {
        this.carModelList = res.rows || []
      })
      listRoadCondition().then(res => {
        this.roadList = res.rows || []
      })
    }
  }
}
</script>
