<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <div class="search-form-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item label="客户" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="车型" prop="carModelId">
          <el-select
            v-model="queryParams.carModelId"
            placeholder="请选择车型"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in carModelList"
              :key="item.id"
              :label="item.modelName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="路况" prop="roadConditionId">
          <el-select
            v-model="queryParams.roadConditionId"
            placeholder="请选择路况"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in roadList"
              :key="item.id"
              :label="item.conditionName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="—"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item class="search-buttons">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <el-button type="success" icon="el-icon-edit" :disabled="!selectedRows.length" @click="handleBatchEdit">修改</el-button>
        <el-button type="danger" icon="el-icon-delete" :disabled="!selectedRows.length" @click="handleBatchDelete">删除</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="list"
        v-loading="loading"
        border
        stripe
        @selection-change="handleSelectionChange"
        class="data-table"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="80" align="center">
          <template slot-scope="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="客户" prop="customerName" min-width="120" align="center" />
        <el-table-column label="车型" prop="carModelName" min-width="120" align="center" />
        <el-table-column label="路况" prop="roadConditionName" min-width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getRoadConditionType(scope.row.roadConditionName)">
              {{ scope.row.roadConditionName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createdAt" width="180" align="center" />
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              class="edit-btn"
            >
              修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              class="delete-btn"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>

    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="500px">
      <el-form :model="form" label-width="80px">
        <el-form-item label="客户">
          <el-input v-model="form.customerName"/>
        </el-form-item>

        <el-form-item label="车型">
          <el-select v-model="form.carModelId">
            <el-option
              v-for="item in carModelList"
              :key="item.id"
              :label="item.modelName"
              :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-form-item label="路况">
          <el-select v-model="form.roadConditionId">
            <el-option
              v-for="item in roadList"
              :key="item.id"
              :label="item.conditionName"
              :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-form-item label="试驾数据">
          <el-upload
            action="/common/upload"
            :on-success="handleUploadSuccess"
            :show-file-list="false">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
          <div v-if="form.testDataUrl">{{ form.testDataUrl }}</div>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="open=false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/web/testDrive'
import { listCarModel } from '@/api/web/carModel'
import { listRoadCondition } from '@/api/web/roadCondition'

export default {
  name: 'TestDrive',
  data() {
    return {
      // 表格数据
      list: [],
      loading: false,
      total: 0,
      selectedRows: [],
      // 弹窗相关
      open: false,
      title: '',
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: undefined,
        carModelId: undefined,
        roadConditionId: undefined,
        startDate: undefined,
        endDate: undefined
      },
      // 时间范围
      dateRange: [],
      // 下拉选项
      carModelList: [],
      roadList: []
    }
  },
  created() {
    this.getList()
    this.loadSelect()
  },
  methods: {
    /** 加载下拉选项 */
    loadSelect() {
      listCarModel().then(res => {
        this.carModelList = res.rows || []
      })
      listRoadCondition().then(res => {
        this.roadList = res.rows || []
      })
    },

    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理时间范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startDate = this.dateRange[0]
        this.queryParams.endDate = this.dateRange[1]
      } else {
        this.queryParams.startDate = undefined
        this.queryParams.endDate = undefined
      }

      listTestDrive(this.queryParams).then(res => {
        this.list = res.rows || []
        this.total = res.total || 0
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        customerName: undefined,
        carModelId: undefined,
        roadConditionId: undefined,
        startDate: undefined,
        endDate: undefined
      }
      this.$refs.queryForm.resetFields()
      this.getList()
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    /** 分页相关 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },

    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },

    /** 获取路况标签类型 */
    getRoadConditionType(roadCondition) {
      const typeMap = {
        '高速路况': 'success',
        '城市路况': 'primary',
        '山路路况': 'warning',
        '恶劣路况': 'danger'
      }
      return typeMap[roadCondition] || 'info'
    },
    handleAdd() {
      this.form = {}
      this.title = '新增试驾记录'
      this.open = true
    },
    handleEdit(row) {
      this.form = { ...row }
      this.title = '编辑试驾记录'
      this.open = true
    },
    submitForm() {
      const api = this.form.id ? updateTestDrive : addTestDrive
      api(this.form).then(() => {
        this.$message.success('保存成功')
        this.open = false
        this.getList()
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该记录吗？', '提示').then(() => {
        delTestDrive(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    handleUploadSuccess(res) {
      this.form.testDataUrl = res.url
    },

    /** 批量修改 */
    handleBatchEdit() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning('请选择一条记录进行修改')
        return
      }
      this.handleEdit(this.selectedRows[0])
    },

    /** 批量删除 */
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录')
        return
      }

      const ids = this.selectedRows.map(row => row.id)
      this.$confirm(`确认删除选中的${ids.length}条记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里需要调用批量删除的API
        Promise.all(ids.map(id => delTestDrive(id))).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 搜索表单样式 */
.search-form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .search-form {
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }
    }

    .search-buttons {
      .el-button {
        margin-left: 10px;

        &.el-button--primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          }
        }
      }
    }
  }

  .action-buttons {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;

    .el-button {
      margin-right: 10px;

      &.el-button--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
      }

      &.el-button--success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
      }

      &.el-button--danger {
        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        border: none;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

/* 表格容器样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .data-table {
    border: none;

    ::v-deep .el-table__header-wrapper {
      .el-table__header {
        th {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #fff;
          font-weight: 600;
          border: none;

          .cell {
            padding: 12px 0;
          }
        }
      }
    }

    ::v-deep .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f8f9ff;
          }

          td {
            border-bottom: 1px solid #f0f2f5;

            .cell {
              padding: 12px 0;
            }
          }
        }
      }
    }

    .edit-btn {
      color: #409eff;

      &:hover {
        color: #66b1ff;
        background-color: #ecf5ff;
      }
    }

    .delete-btn {
      color: #f56c6c;

      &:hover {
        color: #f78989;
        background-color: #fef0f0;
      }
    }
  }

  /* 分页样式 */
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #f0f2f5;
    background-color: #fafbfc;

    ::v-deep .el-pagination {
      .el-pager li {
        &.active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #fff;
          border-radius: 4px;
        }

        &:hover {
          color: #667eea;
        }
      }

      .btn-prev,
      .btn-next {
        &:hover {
          color: #667eea;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form-container {
    .search-form {
      .el-form-item {
        margin-right: 0;
        width: 100%;

        .el-input,
        .el-select,
        .el-date-picker {
          width: 100% !important;
        }
      }
    }

    .action-buttons {
      .el-button {
        margin-bottom: 10px;
        width: 100%;
      }
    }
  }

  .table-container {
    .data-table {
      font-size: 12px;
    }

    .pagination-container {
      text-align: center;

      ::v-deep .el-pagination {
        .el-pagination__sizes,
        .el-pagination__jump {
          display: none;
        }
      }
    }
  }
}
</style>
