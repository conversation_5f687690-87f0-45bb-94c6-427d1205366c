<template>
  <div class="app-container">
    <!-- 查询 -->
    <el-form :model="queryParams" inline>
      <el-form-item label="客户">
        <el-input v-model="queryParams.customerName" placeholder="客户名称"/>
      </el-form-item>

      <el-form-item label="车型">
        <el-select v-model="queryParams.carModelId" clearable>
          <el-option
            v-for="item in carModelList"
            :key="item.id"
            :label="item.modelName"
            :value="item.id"/>
        </el-select>
      </el-form-item>

      <el-form-item label="路况">
        <el-select v-model="queryParams.roadConditionId" clearable>
          <el-option
            v-for="item in roadList"
            :key="item.id"
            :label="item.conditionName"
            :value="item.id"/>
        </el-select>
      </el-form-item>

      <el-button type="primary" @click="getList">搜索</el-button>
      <el-button type="success" @click="handleAdd">新增</el-button>
    </el-form>

    <!-- 表格 -->
    <el-table :data="list" border style="margin-top: 20px">
      <el-table-column label="客户" prop="customerName"/>
      <el-table-column label="车型" prop="carModelName"/>
      <el-table-column label="路况" prop="roadConditionName"/>
      <el-table-column label="试驾数据">
        <template slot-scope="scope">
          <el-link v-if="scope.row.testDataUrl"
                   :href="scope.row.testDataUrl"
                   target="_blank">查看</el-link>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdAt"/>
      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="500px">
      <el-form :model="form" label-width="80px">
        <el-form-item label="客户">
          <el-input v-model="form.customerName"/>
        </el-form-item>

        <el-form-item label="车型">
          <el-select v-model="form.carModelId">
            <el-option
              v-for="item in carModelList"
              :key="item.id"
              :label="item.modelName"
              :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-form-item label="路况">
          <el-select v-model="form.roadConditionId">
            <el-option
              v-for="item in roadList"
              :key="item.id"
              :label="item.conditionName"
              :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-form-item label="试驾数据">
          <el-upload
            action="/common/upload"
            :on-success="handleUploadSuccess"
            :show-file-list="false">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
          <div v-if="form.testDataUrl">{{ form.testDataUrl }}</div>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="open=false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTestDrive, addTestDrive, updateTestDrive, delTestDrive } from '@/api/system/testDrive'
import { listCarModel } from '@/api/system/carModel'
import { listRoadCondition } from '@/api/system/roadCondition'

export default {
  name: 'TestDrive',
  data() {
    return {
      list: [],
      open: false,
      title: '',
      form: {},
      queryParams: {},
      carModelList: [],
      roadList: []
    }
  },
  created() {
    this.getList()
    this.loadSelect()
  },
  methods: {
    loadSelect() {
      listCarModel().then(res => this.carModelList = res.rows)
      listRoadCondition().then(res => this.roadList = res.rows)
    },
    getList() {
      listTestDrive(this.queryParams).then(res => {
        this.list = res.rows
      })
    },
    handleAdd() {
      this.form = {}
      this.title = '新增试驾记录'
      this.open = true
    },
    handleEdit(row) {
      this.form = { ...row }
      this.title = '编辑试驾记录'
      this.open = true
    },
    submitForm() {
      const api = this.form.id ? updateTestDrive : addTestDrive
      api(this.form).then(() => {
        this.$message.success('保存成功')
        this.open = false
        this.getList()
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该记录吗？', '提示').then(() => {
        delTestDrive(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    handleUploadSuccess(res) {
      this.form.testDataUrl = res.url
    }
  }
}
</script>
